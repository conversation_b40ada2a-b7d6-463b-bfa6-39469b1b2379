<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;
use app\admin\model\vppz\App as AppModel;
use app\admin\model\vppz\Order as OrderModel;

/**
 * 自动退款定时任务
 * 用于处理超过自动退款时间的订单，将其标记为不可自动退款
 */
class AutoRefund extends Command
{
    protected function configure()
    {
        $this->setName('autorefund')
            ->setDescription('自动退款定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行自动退款检查任务...');
        
        try {
            $this->processAutoRefund();
            $output->writeln('自动退款检查任务执行完成');
        } catch (\Exception $e) {
            $output->writeln('自动退款检查任务执行失败: ' . $e->getMessage());
            Log::error('自动退款任务执行失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理自动退款逻辑
     */
    private function processAutoRefund()
    {
        $AppModel = new AppModel();
        $OrderModel = new OrderModel();
        
        // 获取所有应用配置
        $apps = $AppModel->select();
        
        foreach ($apps as $app) {
            $auto_refund_hours = intval($app['auto_refund_hours'] ?? 2);
            
            // 如果设置为0，表示关闭自动退款功能，跳过
            if ($auto_refund_hours <= 0) {
                continue;
            }
            
            // 计算自动退款截止时间
            $refund_deadline = time() - ($auto_refund_hours * 3600);
            
            // 查找需要处理的订单：
            // 1. 已支付(status=20)
            // 2. 已付款(pay>0)  
            // 3. 未退款(refund_status=0)
            // 4. 支付时间超过自动退款时间限制
            $orders = $OrderModel->where([
                'app_id' => $app['id'],
                'status' => 20,
                'refund_status' => 0
            ])
            ->where('pay', '>', 0)
            ->where('pay_time', '<', $refund_deadline)
            ->select();
            
            foreach ($orders as $order) {
                // 这里不做实际退款，只是标记订单状态
                // 实际的退款操作仍需要管理员在后台手动处理
                Log::info("订单 {$order['id']} 已超过自动退款时间限制({$auto_refund_hours}小时)，需要手动处理退款");
                
                // 可以在这里添加通知管理员的逻辑
                // 比如发送邮件、短信等
            }
            
            Log::info("应用 {$app['id']} 自动退款检查完成，处理了 " . count($orders) . " 个订单");
        }
    }
}
